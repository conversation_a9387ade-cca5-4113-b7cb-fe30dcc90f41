### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的upsert方法
### 基于表单界面数据构建的真实测试场景

### 环境变量配置
@baseUrl = http://localhost:8080
@contentType = application/json

### ========================================
### 1. 正常新增场景测试 - 小规模纳税人 + 税务/国税
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 2,
  "accountingPeriodStart": "2025-01",
  "accountingPeriodEnd": "2025-12",
  "localTaxAccount": "test_local_account_001",
  "contactName": "张三",
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "请按账期提供银行流水（对账单和回单）、进销票、补账期间的个税明细报表明细的三大报表、余额表、固定资产明细表、无形资产明细表、库存表支持jpg、png、pdf、word、xls",
  "syncReassignment": false,
  "modifyDueDate": false,
  "ddl": "2025-12-31",
  "orgId": 1,
  "status": "DRAFT"
}

### ========================================
### 2. 正常新增场景测试 - 一般纳税人 + 社医保
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "一般纳税人测试公司",
  "creditCode": "91***************X",
  "taxNo": "***************",
  "taxpayerType": 2,
  "valueAddedItemType": 1,
  "accountingPeriodStart": "2025-01",
  "accountingPeriodEnd": "2025-12",
  "localTaxAccount": "general_taxpayer_account",
  "reporterName": "李四",
  "contactInfo": "***********",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "社医保相关材料提交要求",
  "syncReassignment": true,
  "modifyDueDate": false,
  "ddl": "2025-06-30",
  "orgId": 2,
  "status": "PENDING_CONFIRMATION"
}

### ========================================
### 3. 正常更新场景测试 - 包含ID的更新操作
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "id": 1,
  "deliveryOrderNo": "VAD2501271430123",
  "customerName": "更新测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 3,
  "accountingPeriodStart": "2025-01",
  "accountingPeriodEnd": "2025-12",
  "localTaxAccount": "updated_account_001",
  "reporterName": "王五",
  "contactInfo": "***********",
  "syncHandlingFee": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "SPECIAL_INDUSTRY"]
  },
  "requirements": "更新后的交付要求",
  "syncReassignment": false,
  "modifyDueDate": true,
  "ddl": "2025-11-30",
  "orgId": 1,
  "status": "DELIVERY_COMPLETED"
}

### ========================================
### 4. 参数验证测试 - 客户名称为空
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 5. 参数验证测试 - 信用代码格式错误
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "格式错误测试公司",
  "creditCode": "invalid_credit_code",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 6. 参数验证测试 - 纳税性质超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "纳税性质错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 5,
  "valueAddedItemType": 1
}

### ========================================
### 7. 参数验证测试 - 增值事项超出范围
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "增值事项错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 10
}

### ========================================
### 8. 边界条件测试 - 最小有效数据
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "最小数据测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1
}

### ========================================
### 9. 边界条件测试 - 最大长度字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制这是一个非常长的公司名称用来测试最大长度限制",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemType": 1,
  "requirements": "这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制这是一个非常长的交付要求用来测试最大长度限制"
}

### ========================================
### 10. 完整业务场景测试 - 个税账号类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}

{
  "customerName": "个税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemType": 4,
  "accountingPeriodStart": "2025-01",
  "accountingPeriodEnd": "2025-12",
  "localTaxAccount": "personal_tax_account",
  "reporterName": "赵六",
  "contactInfo": "***********",
  "syncHandlingFee": false,
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "个税账号相关材料要求",
  "syncReassignment": true,
  "modifyDueDate": true,
  "ddl": "2025-03-31",
  "orgId": 3,
  "status": "SUBMITTED_PENDING_DELIVERY"
}

### ========================================
### 11. 测试生成交付单编号接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo

### ========================================
### 12. 测试根据交付单编号查询接口
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2501271430123

### ========================================
### 测试说明
### ========================================
# 1. 测试前请确保服务已启动，端口为8080
# 2. 可根据实际环境修改@baseUrl变量
# 3. 测试用例1-2为正常新增场景，应返回200状态码
# 4. 测试用例3为更新场景，需要先执行新增获取有效ID
# 5. 测试用例4-7为参数验证测试，应返回400状态码和相应错误信息
# 6. 测试用例8-10为边界条件和业务场景测试
# 7. 测试用例11-12为相关接口的功能测试
# 8. 所有测试数据基于表单界面的真实业务场景构建
# 9. accountingInfo字段结构说明：
#     - mainType: 账务主类型（STANDARD-标准账务，NON_STANDARD-非标账务）
#     - subTypes: 账务子类型列表（仅当mainType为NON_STANDARD时需要提供）
#       可选值：HIGH_TECH-高新账，VOUCHER_BASED-凭票入账，SPECIAL_INDUSTRY-特殊行业，NON_PROFIT-民非
# 10. status字段使用ValueAddedDeliveryOrderStatus枚举值：
#     - DRAFT: 草稿状态（默认初始状态）
#     - PENDING_CONFIRMATION: 已交付待确认
#     - SUBMITTED_PENDING_DELIVERY: 已提交待交付
#     - DELIVERY_COMPLETED: 交付完成
#     - 其他可用状态请参考ValueAddedDeliveryOrderStatus枚举类
# 11. 可根据实际业务需求调整测试数据和场景
