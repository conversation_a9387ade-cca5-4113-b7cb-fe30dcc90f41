package com.bxm.customer.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账务子类型枚举（非标账务下）
 */
@Getter
@AllArgsConstructor
public enum AccountSubType {

    HIGH_TECH("高新账"),
    VOUCHER_BASED("凭票入账"),
    SPECIAL_INDUSTRY("特殊行业"),
    NON_PROFIT("民非");

    private final String description;

    @JsonValue
    public String getName() {
        return this.name();
    }

    @JsonCreator
    public static AccountSubType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        try {
            return AccountSubType.valueOf(name.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的账务子类型名称: " + name);
        }
    }
}
