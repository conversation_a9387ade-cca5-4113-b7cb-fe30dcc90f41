package com.bxm.customer.domain.vo.valueAdded;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 增值交付单VO
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("增值交付单VO")
public class ValueAddedDeliveryOrderVO {

    /** 主键ID */
    @ApiModelProperty(value = "自增 id")
    private Long id;

    /** 交付单编号（必须，以此为主键） */
    @ApiModelProperty(value = "交付单编号，可选，系统自动生成")
    private String deliveryOrderNo;

    /** 客户ID */
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 客户企业名称 */
    @NotBlank(message = "客户企业名称不能为空")
    @Size(max = 200, message = "客户企业名称长度不能超过200个字符")
    @ApiModelProperty(value = "客户企业名称", required = true)
    private String customerName;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", message = "统一社会信用代码格式不正确")
    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String creditCode;

    /** 税号 */
    @Size(max = 50, message = "税号长度不能超过50个字符")
    @ApiModelProperty(value = "税号")
    private String taxNo;

    /** 纳税性质，1-小规模纳税人，2-一般纳税人 */
    @NotNull(message = "纳税性质不能为空")
    @Min(value = 1, message = "纳税性质必须为1或2")
    @Max(value = 2, message = "纳税性质必须为1或2")
    @ApiModelProperty(value = "纳税性质：1-小规模纳税人，2-一般纳税人", required = true, allowableValues = "1,2")
    private Integer taxpayerType;

    /** 增值事项 */
    @NotNull(message = "增值事项不能为空")
    @Min(value = 1, message = "增值事项必须为1-4之间的数字")
    @Max(value = 4, message = "增值事项必须为1-4之间的数字")
    @ApiModelProperty(value = "增值事项：1-社医保，2-个税明细，3-国税账号，4-个税账号", required = true, allowableValues = "1,2,3,4")
    private Integer valueAddedItemType;

    /** 账期开始时间 */
    @ApiModelProperty(value = "账期开始时间")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private LocalDate accountingPeriodStart;

    /** 账期结束时间 */
    @ApiModelProperty(value = "账期结束时间")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private LocalDate accountingPeriodEnd;

    /** 地税账号 */
    @Size(max = 100, message = "地税账号长度不能超过100个字符")
    @ApiModelProperty(value = "地税账号")
    private String localTaxAccount;

    /** 联络人姓名 */
    @Size(max = 100, message = "联络人姓名长度不能超过100个字符")
    @ApiModelProperty(value = "联络人姓名")
    private String contactName;

    /** 联络人手机号 */
    @Size(max = 50, message = "联络人手机号长度不能超过50个字符")
    @ApiModelProperty(value = "联络人手机号")
    private String contactMobile;

    /** 联络人证件号 */
    @Size(max = 18, message = "联络人证件号长度不能超过18个字符")
    @ApiModelProperty(value = "联络人证件号")
    private String contactIdNumber;

    /** 是否同步手续费，0-否，1-是 */
    @ApiModelProperty(value = "是否同步手续费：0-否，1-是", allowableValues = "0,1")
    private Boolean syncHandlingFee;

    /** 账务类型信息 */
    @ApiModelProperty(value = "账务类型信息")
    @Valid
    private AccountingInfoVO accountingInfo;

    /** 交付要求 */
    @Size(max = 1000, message = "交付要求长度不能超过1000个字符")
    @ApiModelProperty(value = "交付要求")
    private String requirements;

    /** 是否同步改派，0-否，1-是 */
    @ApiModelProperty(value = "是否同步改派：0-否，1-是", allowableValues = "0,1")
    private Boolean syncReassignment;

    /** 是否修改工期，0-否，1-是 */
    @ApiModelProperty(value = "是否修改工期：0-否，1-是", allowableValues = "0,1")
    private Boolean modifyDueDate;

    /** 交付截止日期 */
    @ApiModelProperty(value = "交付截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    /** 发起组织ID */
    @ApiModelProperty(value = "发起组织ID")
    private Long orgId;

    /** 交付状态 */
    @Size(max = 20, message = "交付状态长度不能超过20个字符")
    @ApiModelProperty(value = "交付状态")
    private String status;
}
